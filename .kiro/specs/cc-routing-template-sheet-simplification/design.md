# Design Document

## Overview

This design document outlines the changes needed to simplify the CC routing template generation by using the default "Sheet1" instead of creating a separate "Template" sheet. The modification is focused on the `generateExcelTemplateWithValidation` method in the CC routing service, ensuring minimal code changes while maintaining all existing functionality.

## Architecture

The change affects only the Excel generation logic within the CC routing service. The overall architecture remains unchanged:

- **Service Layer**: `CCRoutingServiceImpl.generateExcelTemplateWithValidation()` - Modified to use "Sheet1"
- **Template Generation Flow**: Unchanged - still generates headers, data, validation, and instructions
- **File Output**: Unchanged - still returns byte array and filename
- **API Layer**: Unchanged - no modifications needed to calling code

## Components and Interfaces

### Modified Component

**CCRoutingServiceImpl.generateExcelTemplateWithValidation()**
- **Current Behavior**: 
  - Deletes default "Sheet1" 
  - Creates new "Template" sheet
  - Sets "Template" as active sheet
- **New Behavior**:
  - Uses default "Sheet1" directly
  - No sheet deletion or creation needed
  - "Sheet1" remains as active sheet

### Unchanged Components

- **Template Generation Methods**: `generateShopGroupTemplate()`, `generateCategoryTemplate()`, `generateWeightCategoryTemplate()`
- **Data Validation Logic**: Dropdown creation, CC options, validation rules
- **Styling Logic**: Header formatting, Default row styling, column widths
- **Instructions Sheet**: Creation and content remain identical
- **File Export Logic**: Byte array generation and filename handling

## Data Models

No changes to data models are required. All existing structures remain the same:
- `CCListItem` - Unchanged
- Excel file structure - Only sheet name changes from "Template" to "Sheet1"
- Validation rules and dropdown options - Unchanged

## Error Handling

Error handling remains completely unchanged:
- Excel creation errors still handled the same way
- File write errors still return `srerr.FormatErr`
- Invalid routing type errors still return `srerr.ParamErr`
- CC API errors still wrapped with `srerr.CCRoutingFailed`

## Testing Strategy

### Unit Tests
- **Test Case 1**: Verify "Sheet1" is used instead of "Template" sheet
  - Generate template and verify active sheet name is "Sheet1"
  - Verify no "Template" sheet exists in the workbook
  
- **Test Case 2**: Verify all existing functionality is preserved
  - Test headers are correctly written to "Sheet1"
  - Test data validation dropdowns work on "Sheet1"
  - Test styling is applied correctly to "Sheet1"
  - Test Instructions sheet is still created

- **Test Case 3**: Verify all template types work with new sheet structure
  - Test shop group template generation
  - Test category template generation  
  - Test weight category template generation

### Integration Tests
- **Test Case 4**: End-to-end template generation and download
  - Call GenerateTemplate API for each routing type
  - Verify downloaded Excel files have correct sheet structure
  - Verify files can be opened and used for import

### Regression Tests
- **Test Case 5**: Verify existing import functionality still works
  - Generate templates with new sheet structure
  - Use generated templates for actual rule imports
  - Verify import process handles "Sheet1" correctly

## Implementation Details

### Code Changes Required

**File**: `internal/usecase/cc_routing/service.go`
**Method**: `generateExcelTemplateWithValidation()`

**Current Code Block**:
```go
file := excelize.NewFile()
sheetName := "Template"

// 删除默认的Sheet1，创建新的模板表
file.DeleteSheet("Sheet1")
file.NewSheet(sheetName)
file.SetActiveSheet(file.GetSheetIndex(sheetName))
```

**New Code Block**:
```go
file := excelize.NewFile()
sheetName := "Sheet1"

// 使用默认的Sheet1作为模板表
// 不需要删除或创建新sheet，直接使用默认的Sheet1
```

### Variable Usage
- `sheetName` variable changes from `"Template"` to `"Sheet1"`
- All subsequent references to `sheetName` automatically use "Sheet1"
- No other variable changes needed

### Method Signature
No changes to method signature:
```go
func (c *CCRoutingServiceImpl) generateExcelTemplateWithValidation(
    filename string, 
    headers []string, 
    rows [][]string, 
    validateColumnIndex int, 
    ccList []*CCListItem
) ([]byte, string, *srerr.Error)
```

## Validation and Quality Assurance

### Pre-deployment Validation
1. **Template Generation**: Verify all three template types generate correctly
2. **Sheet Structure**: Confirm "Sheet1" contains all expected content
3. **Data Validation**: Test dropdown functionality in generated templates
4. **File Compatibility**: Ensure Excel files open correctly in Excel/LibreOffice
5. **Import Compatibility**: Verify existing import logic handles "Sheet1" correctly

### Backward Compatibility
- **File Format**: Excel files remain .xlsx format
- **Content Structure**: Headers, data, and validation rules unchanged
- **API Response**: Byte array and filename format unchanged
- **Import Process**: Should work seamlessly with "Sheet1" instead of "Template"

## Risk Assessment

### Low Risk Areas
- **Functional Logic**: No changes to business logic or validation rules
- **Data Processing**: No changes to how data is processed or formatted
- **API Interface**: No changes to method signatures or return types

### Minimal Risk Areas  
- **Sheet Naming**: Simple string change from "Template" to "Sheet1"
- **Excel Library Usage**: Using standard excelize functionality
- **Import Compatibility**: Import logic should handle sheet name transparently

### Mitigation Strategies
- **Thorough Testing**: Test all template types and import scenarios
- **Gradual Rollout**: Deploy to staging environment first
- **Rollback Plan**: Simple code revert if issues discovered
- **Monitoring**: Monitor template generation and import success rates