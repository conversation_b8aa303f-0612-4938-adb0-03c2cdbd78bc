# Implementation Plan

- [ ] 1. Modify template generation method to use Sheet1
  - Update the `generateExcelTemplateWithValidation` method in `internal/usecase/cc_routing/service.go`
  - Change sheet name from "Template" to "Sheet1"
  - Remove sheet deletion and creation logic
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 2. Create unit tests for Sheet1 template generation
  - Write test to verify "Sheet1" is used as the active sheet
  - Write test to verify no "Template" sheet exists in generated workbook
  - Write test to verify all existing functionality (headers, validation, styling) works with "Sheet1"
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

- [ ] 3. Create integration tests for all template types
  - Test shop group template generation with new sheet structure
  - Test category template generation with new sheet structure  
  - Test weight category template generation with new sheet structure
  - Verify generated Excel files can be opened and contain correct content
  - _Requirements: 1.1, 1.2, 1.3, 3.3_

- [ ] 4. Verify import compatibility with Sheet1 templates
  - Test that existing import methods can process templates generated with "Sheet1"
  - Verify `ImportShopGroupRules`, `ImportCategoryRules`, and `ImportWeightCategoryRules` work correctly
  - Ensure no breaking changes to import functionality
  - _Requirements: 2.1, 2.2, 2.3, 3.4_