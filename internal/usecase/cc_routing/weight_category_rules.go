package cc_routing

import (
	"context"
	"strconv"
	"strings"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/fileutil"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

// parseWeightCategoryRules 解析重量+类目规则数据，从导入和更新方法中抽取的共用逻辑
func (c *CCRoutingServiceImpl) parseWeightCategoryRules(ctx context.Context, rows []map[string]string) ([]cc_routing_rule.WeightCategoryRuleItem, string, *srerr.Error) {
	rules := make([]cc_routing_rule.WeightCategoryRuleItem, 0, len(rows))
	var defaultCC string
	hasDefault := false

	for i, row := range rows {
		categoryIdStr := strings.TrimSpace(row["Global Category ID"])
		minWeightStr := strings.TrimSpace(row["Weight Min (Not included)"])
		maxWeightStr := strings.TrimSpace(row["Weight Max (Included)"])
		allocateTo := strings.TrimSpace(row["Allocate to"])

		if categoryIdStr == "" || allocateTo == "" {
			return nil, "", srerr.New(srerr.ParamErr, nil, "row %d: Global Category ID='%s' and Allocate to='%s' cannot be empty", i+1, categoryIdStr, allocateTo)
		}

		// 检查是否是默认行
		if strings.ToLower(categoryIdStr) == "default" {
			hasDefault = true
			defaultCC = allocateTo
		} else {
			if minWeightStr == "" || maxWeightStr == "" {
				return nil, "", srerr.New(srerr.ParamErr, nil, "row %d: Weight Min='%s' and Weight Max='%s' cannot be empty for non-default rows (Global Category ID='%s')",
					i+1, minWeightStr, maxWeightStr, categoryIdStr)
			}

			categoryId, parseErr := strconv.Atoi(categoryIdStr)
			if parseErr != nil {
				return nil, "", srerr.New(srerr.ParamErr, nil, "row %d: invalid Global Category ID '%s'", i+1, categoryIdStr)
			}

			minWeight, parseErr := strconv.Atoi(minWeightStr)
			if parseErr != nil {
				return nil, "", srerr.New(srerr.ParamErr, nil, "row %d: invalid Weight Min '%s'", i+1, minWeightStr)
			}

			maxWeight, parseErr := strconv.Atoi(maxWeightStr)
			if parseErr != nil {
				return nil, "", srerr.New(srerr.ParamErr, nil, "row %d: invalid Weight Max '%s'", i+1, maxWeightStr)
			}

			if minWeight >= maxWeight {
				return nil, "", srerr.New(srerr.ParamErr, nil, "row %d: Weight Min (%d) must be less than Weight Max (%d) for Global Category ID='%s'",
					i+1, minWeight, maxWeight, categoryIdStr)
			}

			rules = append(rules, cc_routing_rule.WeightCategoryRuleItem{
				CategoryId:       categoryId,
				MinWeight:        minWeight,
				MaxWeight:        maxWeight,
				CustomsClearance: allocateTo,
			})
		}
	}

	if !hasDefault {
		return nil, "", srerr.New(srerr.ParamErr, nil, "missing required 'default' row")
	}

	// 验证重量区间连续性（按类目分组）
	if err := c.validateWeightRanges(rules); err != nil {
		return nil, "", err
	}

	// 强制CC值校验（新API默认安全）
	ccValues := make([]string, 0, len(rules)+1)
	for _, rule := range rules {
		ccValues = append(ccValues, rule.CustomsClearance)
	}
	if defaultCC != "" {
		ccValues = append(ccValues, defaultCC)
	}
	if err := c.validateCCValues(ctx, ccValues, true); err != nil {
		return nil, "", err
	}

	return rules, defaultCC, nil
}

// ImportWeightCategoryRules 导入重量+类目规则
func (c *CCRoutingServiceImpl) ImportWeightCategoryRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	// 解析Excel/CSV文件
	rows, fileErr := fileutil.ParseExcelToMap(fileData)
	if fileErr != nil {
		return srerr.With(srerr.FormatErr, "parse file", fileErr)
	}

	if len(rows) == 0 {
		return srerr.New(srerr.ParamErr, nil, "empty file or no data rows")
	}

	// 调用共用的解析逻辑
	rules, defaultCC, parseErr := c.parseWeightCategoryRules(ctx, rows)
	if parseErr != nil {
		return parseErr
	}

	// 构建规则详情
	ruleDetail := cc_routing_rule.CCRoutingRuleDetail{
		WeightCategoryRuleDetail: cc_routing_rule.WeightCategoryRuleDetail{
			Rules:                   rules,
			DefaultCustomsClearance: defaultCC,
		},
	}

	// 如果只是验证模式，到此为止，不实际保存
	if validateOnly {
		logger.CtxLogInfof(ctx, "Weight+Category rules validation passed for productId=%d (validate-only mode)", productId)
		return nil
	}

	// 创建或更新规则
	return c.createOrUpdateRule(ctx, productId, cc_routing_rule.CCRoutingTypeWeightCategory, ruleDetail, operator)
}

// UpdateWeightCategoryRules 更新重量+类目规则
func (c *CCRoutingServiceImpl) UpdateWeightCategoryRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	// 先检查规则是否存在
	_, err := c.GetCCRoutingRuleById(ctx, id)
	if err != nil {
		return err
	}

	// 解析Excel/CSV文件
	rows, fileErr := fileutil.ParseExcelToMap(fileData)
	if fileErr != nil {
		return srerr.With(srerr.FormatErr, "parse file", fileErr)
	}

	if len(rows) == 0 {
		return srerr.New(srerr.ParamErr, nil, "empty file or no data rows")
	}

	// 调用共用的解析逻辑
	rules, defaultCC, parseErr := c.parseWeightCategoryRules(ctx, rows)
	if parseErr != nil {
		return parseErr
	}

	// 如果只是验证，到这里就返回
	if validateOnly {
		logger.CtxLogInfof(ctx, "Weight+Category rules validation passed for ruleId=%d, productId=%d (validate-only mode)", id, productId)
		return nil
	}

	// 更新规则
	ruleDetail := cc_routing_rule.CCRoutingRuleDetail{
		WeightCategoryRuleDetail: cc_routing_rule.WeightCategoryRuleDetail{
			Rules:                   rules,
			DefaultCustomsClearance: defaultCC,
		},
	}

	// 更新规则
	ruleObj := &cc_routing_rule.CCRoutingRule{
		Id:          id,
		ProductId:   productId,
		RoutingType: cc_routing_rule.CCRoutingTypeWeightCategory,
		RuleDetail:  ruleDetail,
	}
	return c.UpdateCCRoutingRule(ctx, ruleObj, operator)
}
