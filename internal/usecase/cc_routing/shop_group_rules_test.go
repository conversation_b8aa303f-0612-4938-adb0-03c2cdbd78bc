package cc_routing

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/ccclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/client/lpsclient"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/cc_routing_rule"
	rule "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/domain/routing/entity"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
)

func TestParseShopGroupRules(t *testing.T) {
	tests := []struct {
		name           string
		rows           []map[string]string
		expectedRules  []cc_routing_rule.ShopGroupRuleItem
		expectedCC     string
		expectError    bool
		mockCCResponse []*ccclient.CustomsAgent
		mockCCErr      error
	}{
		{
			name: "正常解析带默认行的数据",
			rows: []map[string]string{
				{"Client Group": "group1", "Allocate to": "CC1"},
				{"Client Group": "group2", "Allocate to": "CC2"},
				{"Client Group": "default", "Allocate to": "CC3"},
			},
			expectedRules: []cc_routing_rule.ShopGroupRuleItem{
				{ClientTagId: uint8(lpsclient.ClientTagCCAllocation), ClientGroupId: "group1", CustomsClearance: "CC1"},
				{ClientTagId: uint8(lpsclient.ClientTagCCAllocation), ClientGroupId: "group2", CustomsClearance: "CC2"},
			},
			expectedCC:     "CC3",
			expectError:    false,
			mockCCResponse: []*ccclient.CustomsAgent{{AgentId: "CC1"}, {AgentId: "CC2"}, {AgentId: "CC3"}},
		},
		{
			name: "缺少默认行",
			rows: []map[string]string{
				{"Client Group": "group1", "Allocate to": "CC1"},
				{"Client Group": "group2", "Allocate to": "CC2"},
			},
			expectedRules:  nil,
			expectedCC:     "",
			expectError:    true,
			mockCCResponse: nil, // 不会调用到 CC API
		},
		{
			name: "包含重复的Client Group",
			rows: []map[string]string{
				{"Client Group": "group1", "Allocate to": "CC1"},
				{"Client Group": "group1", "Allocate to": "CC2"},
				{"Client Group": "default", "Allocate to": "CC3"},
			},
			expectedRules:  nil,
			expectedCC:     "",
			expectError:    true,
			mockCCResponse: nil, // 不会调用到 CC API
		},
		{
			name: "包含空值",
			rows: []map[string]string{
				{"Client Group": "", "Allocate to": "CC1"},
				{"Client Group": "default", "Allocate to": "CC3"},
			},
			expectedRules:  nil,
			expectedCC:     "",
			expectError:    true,
			mockCCResponse: nil, // 不会调用到 CC API
		},
		{
			name: "包含无效的CC值",
			rows: []map[string]string{
				{"Client Group": "group1", "Allocate to": "CC1"},
				{"Client Group": "group2", "Allocate to": "INVALID_CC"},
				{"Client Group": "default", "Allocate to": "CC3"},
			},
			expectedRules:  nil,
			expectedCC:     "",
			expectError:    true,
			mockCCResponse: []*ccclient.CustomsAgent{{AgentId: "CC1"}, {AgentId: "CC3"}},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock对象
			mockCCApi := new(MockCCApi)

			// 设置mock行为（只在需要时）
			if tt.mockCCResponse != nil || tt.mockCCErr != nil {
				var srErr *srerr.Error
				if tt.mockCCErr != nil {
					srErr = srerr.New(srerr.CustomsServiceErr, nil, tt.mockCCErr.Error())
				}
				mockCCApi.On("ListCustomsAgents", mock.Anything).Return(tt.mockCCResponse, srErr)
			}

			// 创建服务实例
			service := &CCRoutingServiceImpl{
				CCApi: mockCCApi,
			}

			// 执行测试
			rules, defaultCC, err := service.parseShopGroupRules(context.Background(), tt.rows)

			// 验证结果
			if tt.expectError {
				assert.Error(t, err)
			} else {
				if err != nil {
					t.Errorf("Expected no error, but got: %v", err)
				}
				assert.Equal(t, tt.expectedCC, defaultCC)
				assert.Equal(t, tt.expectedRules, rules)
			}

			// 验证mock调用
			mockCCApi.AssertExpectations(t)
		})
	}
}

func TestImportShopGroupRules(t *testing.T) {
	tests := []struct {
		name           string
		productId      int
		fileContent    []byte
		validateOnly   bool
		mockParseError *srerr.Error
		mockRules      []cc_routing_rule.ShopGroupRuleItem
		mockDefaultCC  string
		mockCreateErr  *srerr.Error
		expectError    bool
	}{
		{
			name:         "成功导入规则",
			productId:    100,
			fileContent:  []byte("test file content"),
			validateOnly: false,
			mockRules: []cc_routing_rule.ShopGroupRuleItem{
				{ClientTagId: 8, ClientGroupId: "group1", CustomsClearance: "CC1"},
			},
			mockDefaultCC: "CC_DEFAULT",
			mockCreateErr: nil,
			expectError:   false,
		},
		{
			name:           "解析文件失败",
			productId:      100,
			fileContent:    []byte("invalid content"),
			validateOnly:   false,
			mockParseError: srerr.New(srerr.FormatErr, nil, "parse error"),
			expectError:    true,
		},
		{
			name:         "仅验证模式成功",
			productId:    100,
			fileContent:  []byte("test file content"),
			validateOnly: true,
			mockRules: []cc_routing_rule.ShopGroupRuleItem{
				{ClientTagId: 8, ClientGroupId: "group1", CustomsClearance: "CC1"},
			},
			mockDefaultCC: "CC_DEFAULT",
			expectError:   false,
		},
		{
			name:         "创建规则失败",
			productId:    100,
			fileContent:  []byte("test file content"),
			validateOnly: false,
			mockRules: []cc_routing_rule.ShopGroupRuleItem{
				{ClientTagId: 8, ClientGroupId: "group1", CustomsClearance: "CC1"},
			},
			mockDefaultCC: "CC_DEFAULT",
			mockCreateErr: srerr.New(srerr.DataErr, nil, "create error"),
			expectError:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试service
			svc := new(ShopGroupRuleTestService)
			svc.t = t
			svc.mockRules = tt.mockRules
			svc.mockDefaultCC = tt.mockDefaultCC
			svc.mockParseError = tt.mockParseError
			svc.mockCreateErr = tt.mockCreateErr
			svc.expectedProductId = tt.productId
			svc.expectedRoutingType = cc_routing_rule.CCRoutingTypeShopGroup

			// 执行测试
			err := svc.ImportShopGroupRules(context.Background(), tt.productId, tt.fileContent, "test_operator", tt.validateOnly)

			// 验证结果
			if tt.expectError {
				assert.Error(t, err)
			} else {
				if err != nil {
					t.Errorf("Expected no error, but got: %v", err)
				}
			}
		})
	}
}

func TestUpdateShopGroupRules(t *testing.T) {
	tests := []struct {
		name           string
		id             int
		productId      int
		fileContent    []byte
		validateOnly   bool
		mockRule       *cc_routing_rule.CCRoutingRule
		mockGetRuleErr *srerr.Error
		mockRules      []cc_routing_rule.ShopGroupRuleItem
		mockDefaultCC  string
		mockUpdateErr  *srerr.Error
		expectError    bool
	}{
		{
			name:         "成功更新规则",
			id:           1,
			productId:    100,
			fileContent:  []byte("test file content"),
			validateOnly: false,
			mockRule: &cc_routing_rule.CCRoutingRule{
				Id:          1,
				ProductId:   100,
				RoutingType: cc_routing_rule.CCRoutingTypeShopGroup,
			},
			mockRules: []cc_routing_rule.ShopGroupRuleItem{
				{ClientTagId: 8, ClientGroupId: "group1", CustomsClearance: "CC1"},
			},
			mockDefaultCC: "CC_DEFAULT",
			mockUpdateErr: nil,
			expectError:   false,
		},
		{
			name:           "获取规则失败",
			id:             1,
			productId:      100,
			fileContent:    []byte("test file content"),
			validateOnly:   false,
			mockGetRuleErr: srerr.New(srerr.DataErr, nil, "not found"),
			expectError:    true,
		},
		{
			name:         "更新不同类型的规则成功",
			id:           1,
			productId:    100,
			fileContent:  []byte("test file content"),
			validateOnly: false,
			mockRule: &cc_routing_rule.CCRoutingRule{
				Id:          1,
				ProductId:   100,
				RoutingType: cc_routing_rule.CCRoutingTypeCategory, // 不同的类型，但应该成功
			},
			mockRules: []cc_routing_rule.ShopGroupRuleItem{
				{ClientTagId: 8, ClientGroupId: "group1", CustomsClearance: "CC1"},
			},
			mockDefaultCC: "CC_DEFAULT",
			mockUpdateErr: nil,
			expectError:   false,
		},
		{
			name:         "更新固定类型规则为店铺分组规则成功",
			id:           2,
			productId:    200,
			fileContent:  []byte("test file content"),
			validateOnly: false,
			mockRule: &cc_routing_rule.CCRoutingRule{
				Id:          2,
				ProductId:   200,
				RoutingType: cc_routing_rule.CCRoutingTypeFixed, // 固定类型，但应该成功更新为店铺分组
			},
			mockRules: []cc_routing_rule.ShopGroupRuleItem{
				{ClientTagId: 8, ClientGroupId: "group2", CustomsClearance: "CC2"},
			},
			mockDefaultCC: "CC_DEFAULT2",
			mockUpdateErr: nil,
			expectError:   false,
		},
		{
			name:         "仅验证模式成功",
			id:           1,
			productId:    100,
			fileContent:  []byte("test file content"),
			validateOnly: true,
			mockRule: &cc_routing_rule.CCRoutingRule{
				Id:          1,
				ProductId:   100,
				RoutingType: cc_routing_rule.CCRoutingTypeShopGroup,
			},
			mockRules:     []cc_routing_rule.ShopGroupRuleItem{{ClientTagId: 8, ClientGroupId: "group1", CustomsClearance: "CC1"}},
			mockDefaultCC: "CC_DEFAULT",
			expectError:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试service
			svc := new(ShopGroupRuleUpdateTestService)
			svc.t = t
			svc.mockRule = tt.mockRule
			svc.mockGetRuleErr = tt.mockGetRuleErr
			svc.mockRules = tt.mockRules
			svc.mockDefaultCC = tt.mockDefaultCC
			svc.mockUpdateErr = tt.mockUpdateErr
			svc.expectedId = tt.id
			svc.expectedProductId = tt.productId
			svc.expectedRoutingType = cc_routing_rule.CCRoutingTypeShopGroup

			// 执行测试
			err := svc.UpdateShopGroupRules(context.Background(), tt.id, tt.productId, tt.fileContent, "test_operator", tt.validateOnly)

			// 验证结果
			if tt.expectError {
				assert.Error(t, err)
			} else {
				if err != nil {
					t.Errorf("Expected no error, but got: %v", err)
				}
			}
		})
	}
}

// ShopGroupRuleTestService 是用于测试的CCRoutingService实现
type ShopGroupRuleTestService struct {
	t *testing.T

	// 模拟数据
	mockRules      []cc_routing_rule.ShopGroupRuleItem
	mockDefaultCC  string
	mockParseError *srerr.Error
	mockCreateErr  *srerr.Error

	// 期望值
	expectedProductId   int
	expectedRoutingType cc_routing_rule.CCRoutingType
}

func (s *ShopGroupRuleTestService) parseShopGroupRules(ctx context.Context, rows []map[string]string) ([]cc_routing_rule.ShopGroupRuleItem, string, *srerr.Error) {
	if s.mockParseError != nil {
		return nil, "", s.mockParseError
	}
	return s.mockRules, s.mockDefaultCC, nil
}

func (s *ShopGroupRuleTestService) createOrUpdateRule(ctx context.Context, productId int, routingType cc_routing_rule.CCRoutingType, ruleDetail cc_routing_rule.CCRoutingRuleDetail, operator string) *srerr.Error {
	assert.Equal(s.t, s.expectedProductId, productId)
	assert.Equal(s.t, s.expectedRoutingType, routingType)
	return s.mockCreateErr
}

// 实现CCRoutingService必要的方法
func (s *ShopGroupRuleTestService) ImportShopGroupRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	// 解析Excel/CSV文件
	// 调用共用的解析逻辑
	rules, defaultCC, parseErr := s.parseShopGroupRules(ctx, nil) // 只用于测试，实际解析交给mock
	if parseErr != nil {
		return parseErr
	}

	// 构建规则详情
	ruleDetail := cc_routing_rule.CCRoutingRuleDetail{
		ShopGroupRuleDetail: cc_routing_rule.ShopGroupRuleDetail{
			Rules:                   rules,
			DefaultCustomsClearance: defaultCC,
		},
	}

	// 如果只是验证模式，到此为止，不实际保存
	if validateOnly {
		return nil
	}

	// 创建或更新规则
	return s.createOrUpdateRule(ctx, productId, cc_routing_rule.CCRoutingTypeShopGroup, ruleDetail, operator)
}

// 未使用的方法，但需要实现接口
func (s *ShopGroupRuleTestService) CCRouting(ctx context.Context, productId int, weight int, ccList []string, shopId int64, categoryIdList []int) (string, *srerr.Error) {
	return "", nil
}
func (s *ShopGroupRuleTestService) CreateCCRoutingRule(ctx context.Context, rule *cc_routing_rule.CCRoutingRule, operator string) (int, *srerr.Error) {
	return 0, nil
}
func (s *ShopGroupRuleTestService) UpdateCCRoutingRule(ctx context.Context, rule *cc_routing_rule.CCRoutingRule, operator string) *srerr.Error {
	return nil
}
func (s *ShopGroupRuleTestService) GetCCRoutingRuleById(ctx context.Context, id int) (*cc_routing_rule.CCRoutingRule, *srerr.Error) {
	return nil, nil
}
func (s *ShopGroupRuleTestService) GetCCRoutingRuleByProductId(ctx context.Context, productId int) (*cc_routing_rule.CCRoutingRule, *srerr.Error) {
	return nil, nil
}
func (s *ShopGroupRuleTestService) DeleteCCRoutingRuleById(ctx context.Context, id int) *srerr.Error {
	return nil
}
func (s *ShopGroupRuleTestService) ListCCRoutingRule(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]cc_routing_rule.CCRoutingRule, int64, *srerr.Error) {
	return nil, 0, nil
}
func (s *ShopGroupRuleTestService) CCFilter(ctx context.Context, orderSn string, availableLanes []*rule.RoutingLaneInfo) ([]*rule.RoutingLaneInfo, *srerr.Error) {
	return nil, nil
}
func (s *ShopGroupRuleTestService) ImportCategoryRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *ShopGroupRuleTestService) UpdateShopGroupRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *ShopGroupRuleTestService) UpdateCategoryRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *ShopGroupRuleTestService) ImportWeightCategoryRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *ShopGroupRuleTestService) UpdateWeightCategoryRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *ShopGroupRuleTestService) GenerateTemplate(ctx context.Context, routingType cc_routing_rule.CCRoutingType) ([]byte, string, *srerr.Error) {
	return nil, "", nil
}
func (s *ShopGroupRuleTestService) ListCCs(ctx context.Context, productId int, category int64, regionId string) ([]*CCListItem, *srerr.Error) {
	return nil, nil
}

// ShopGroupRuleUpdateTestService 是用于测试UpdateShopGroupRules的CCRoutingService实现
type ShopGroupRuleUpdateTestService struct {
	t *testing.T

	// 模拟数据
	mockRule       *cc_routing_rule.CCRoutingRule
	mockGetRuleErr *srerr.Error
	mockRules      []cc_routing_rule.ShopGroupRuleItem
	mockDefaultCC  string
	mockUpdateErr  *srerr.Error

	// 期望值
	expectedId          int
	expectedProductId   int
	expectedRoutingType cc_routing_rule.CCRoutingType
}

func (s *ShopGroupRuleUpdateTestService) GetCCRoutingRuleById(ctx context.Context, id int) (*cc_routing_rule.CCRoutingRule, *srerr.Error) {
	assert.Equal(s.t, s.expectedId, id)
	if s.mockGetRuleErr != nil {
		return nil, s.mockGetRuleErr
	}
	return s.mockRule, nil
}

func (s *ShopGroupRuleUpdateTestService) parseShopGroupRules(ctx context.Context, rows []map[string]string) ([]cc_routing_rule.ShopGroupRuleItem, string, *srerr.Error) {
	return s.mockRules, s.mockDefaultCC, nil
}

func (s *ShopGroupRuleUpdateTestService) UpdateCCRoutingRule(ctx context.Context, rule *cc_routing_rule.CCRoutingRule, operator string) *srerr.Error {
	assert.Equal(s.t, s.expectedId, rule.Id)
	assert.Equal(s.t, s.expectedProductId, rule.ProductId)
	assert.Equal(s.t, s.expectedRoutingType, rule.RoutingType)
	return s.mockUpdateErr
}

// 实现CCRoutingService必要的方法
func (s *ShopGroupRuleUpdateTestService) UpdateShopGroupRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	// 先检查规则是否存在
	_, err := s.GetCCRoutingRuleById(ctx, id)
	if err != nil {
		return err
	}

	// 调用共用的解析逻辑
	rules, defaultCC, _ := s.parseShopGroupRules(ctx, nil)

	// 如果只是验证，到这里就返回
	if validateOnly {
		return nil
	}

	// 更新规则
	ruleDetail := cc_routing_rule.CCRoutingRuleDetail{
		ShopGroupRuleDetail: cc_routing_rule.ShopGroupRuleDetail{
			Rules:                   rules,
			DefaultCustomsClearance: defaultCC,
		},
	}

	// 更新规则
	ruleObj := &cc_routing_rule.CCRoutingRule{
		Id:          id,
		ProductId:   productId,
		RoutingType: cc_routing_rule.CCRoutingTypeShopGroup,
		RuleDetail:  ruleDetail,
	}
	return s.UpdateCCRoutingRule(ctx, ruleObj, operator)
}

// 未使用的方法，但需要实现接口
func (s *ShopGroupRuleUpdateTestService) CCRouting(ctx context.Context, productId int, weight int, ccList []string, shopId int64, categoryIdList []int) (string, *srerr.Error) {
	return "", nil
}
func (s *ShopGroupRuleUpdateTestService) CreateCCRoutingRule(ctx context.Context, rule *cc_routing_rule.CCRoutingRule, operator string) (int, *srerr.Error) {
	return 0, nil
}
func (s *ShopGroupRuleUpdateTestService) GetCCRoutingRuleByProductId(ctx context.Context, productId int) (*cc_routing_rule.CCRoutingRule, *srerr.Error) {
	return nil, nil
}
func (s *ShopGroupRuleUpdateTestService) DeleteCCRoutingRuleById(ctx context.Context, id int) *srerr.Error {
	return nil
}
func (s *ShopGroupRuleUpdateTestService) ListCCRoutingRule(ctx context.Context, condition map[string]interface{}, offset, size int64) ([]cc_routing_rule.CCRoutingRule, int64, *srerr.Error) {
	return nil, 0, nil
}
func (s *ShopGroupRuleUpdateTestService) CCFilter(ctx context.Context, orderSn string, availableLanes []*rule.RoutingLaneInfo) ([]*rule.RoutingLaneInfo, *srerr.Error) {
	return nil, nil
}
func (s *ShopGroupRuleUpdateTestService) ImportCategoryRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *ShopGroupRuleUpdateTestService) ImportShopGroupRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *ShopGroupRuleUpdateTestService) UpdateCategoryRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *ShopGroupRuleUpdateTestService) ImportWeightCategoryRules(ctx context.Context, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *ShopGroupRuleUpdateTestService) UpdateWeightCategoryRules(ctx context.Context, id int, productId int, fileData []byte, operator string, validateOnly bool) *srerr.Error {
	return nil
}
func (s *ShopGroupRuleUpdateTestService) GenerateTemplate(ctx context.Context, routingType cc_routing_rule.CCRoutingType) ([]byte, string, *srerr.Error) {
	return nil, "", nil
}
func (s *ShopGroupRuleUpdateTestService) ListCCs(ctx context.Context, productId int, category int64, regionId string) ([]*CCListItem, *srerr.Error) {
	return nil, nil
}
