package select_lane

import (
	"context"

	pb "git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing-protocol/protocol/go"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/internal/util/srerr"
	"git.garena.com/shopee/bg-logistics/logistics/logistics-smart-routing/pkg/grpc_util"
)

func (s *SmartRoutingServiceImpl) CustomsClearanceRouting(ctx context.Context, req *pb.CustomsClearanceRoutingReq) (*pb.CustomsClearanceRoutingRsp, error) {
	rsp := &pb.CustomsClearanceRoutingRsp{}

	if len(req.GetCustomsClearanceList()) == 0 {
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), srerr.New(srerr.ParamErr, req, "customs clearance list is empty"))
		return rsp, nil
	}

	// use G as unit in system
	var weight int
	switch req.GetWeightUnit() {
	case pb.WeightUnit_G:
		weight = int(req.GetWeight())
	case pb.WeightUnit_KG:
		weight = int(req.GetWeight()) * 1000
	}

	categoryIdList := make([]int, len(req.GetGlobalCategoryIdList()))
	for i, categoryId := range req.GetGlobalCategoryIdList() {
		categoryIdList[i] = int(categoryId)
	}
	ret, err := s.CCRoutingSrv.CCRouting(ctx, int(req.GetProductId()), weight, req.GetCustomsClearanceList(), req.GetShopId(), categoryIdList)
	if err != nil {
		rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), err)
		return rsp, nil
	}

	rsp.Header = grpc_util.GenerateRespHeader(req.GetHeader().GetRequestId(), nil)
	rsp.RoutingResult = proto.String(ret)

	return rsp, nil
}
